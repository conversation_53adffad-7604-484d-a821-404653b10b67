import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/fa';
import { SmartWalletData } from '../SmartWallet';
import ProfitChart from './ProfitChart';

interface SmartWalletTableProps {
  data: SmartWalletData[];
  isLoading: boolean;
}

const SmartWalletTable: React.FC<SmartWalletTableProps> = ({ data, isLoading }) => {
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(decimals);
  };

  const formatPnL = (value: number): { text: string; color: string } => {
    const isPositive = value >= 0;
    const formattedValue = `${isPositive ? '+' : ''}$${formatNumber(Math.abs(value))}`;
    return {
      text: formattedValue,
      color: isPositive ? 'text-green-400' : 'text-red-400'
    };
  };

  const copyToClipboard = (address: string) => {
    navigator.clipboard.writeText(address);
    setCopiedAddress(address);
    setTimeout(() => setCopiedAddress(null), 2000);
  };

  if (isLoading) {
    return (
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-12 border border-gray-800/50">
        <div className="flex items-center justify-center">
          <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
          <span className="text-white text-lg">Loading smart wallet data...</span>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-12 border border-gray-800/50">
        <div className="text-center">
          <div className="text-gray-400 text-lg mb-2">No smart wallets found</div>
          <div className="text-gray-500 text-sm">Try adjusting your filter criteria</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl border border-gray-800/50 overflow-hidden w-full">
      <div className="overflow-x-auto w-full">
        <table className="w-full table-fixed min-w-[1200px]">
          <colgroup>
            <col style={{ width: '20%' }} />
            <col style={{ width: '10%' }} />
            <col style={{ width: '10%' }} />
            <col style={{ width: '10%' }} />
            <col style={{ width: '8%' }} />
            <col style={{ width: '8%' }} />
            <col style={{ width: '8%' }} />
            <col style={{ width: '14%' }} />
            <col style={{ width: '8%' }} />
            <col style={{ width: '4%' }} />
          </colgroup>
          <thead className="bg-[#1A1E24] border-b border-gray-700/50">
            <tr>
              <th className="text-left py-4 px-6 text-xs font-medium text-gray-400 uppercase tracking-wider">
                Wallet/SOL Bal
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                1D PnL
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                7D PnL
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                30D PnL
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                7D Win Rate
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                7D Txs
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                7D Volume
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                7D Profit
              </th>
              <th className="text-right py-4 px-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
                Last Time
              </th>
              <th className="text-right py-4 px-6 text-xs font-medium text-gray-400 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-800/50">
            {data.map((wallet) => {
              const pnl1d = formatPnL(wallet.pnl1d);
              const pnl7d = formatPnL(wallet.pnl7d);
              const pnl30d = formatPnL(wallet.pnl30d);

              return (
                <tr key={wallet.id} className="hover:bg-[#1F2329] transition-colors">
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-[#7FFFD4] to-[#025FDA] rounded-full flex items-center justify-center">
                        <span className="text-black text-xs font-bold">
                          {wallet.walletAddress.slice(0, 2)}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-white font-medium text-sm">{wallet.walletAddress}</span>
                          <button
                            onClick={() => copyToClipboard(wallet.walletAddress)}
                            className="text-gray-400 hover:text-[#7FFFD4] transition-colors"
                          >
                            {copiedAddress === wallet.walletAddress ? (
                              <FaCheck size={10} className="text-green-400" />
                            ) : (
                              <FaCopy size={10} />
                            )}
                          </button>
                        </div>
                        <div className="text-gray-400 text-xs">
                          {formatNumber(wallet.solBalance)} SOL
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className={`py-4 px-4 text-right font-medium text-sm ${pnl1d.color}`}>
                    {pnl1d.text}
                  </td>
                  <td className={`py-4 px-4 text-right font-medium text-sm ${pnl7d.color}`}>
                    {pnl7d.text}
                  </td>
                  <td className={`py-4 px-4 text-right font-medium text-sm ${pnl30d.color}`}>
                    {pnl30d.text}
                  </td>
                  <td className="py-4 px-4 text-right text-white text-sm">
                    {wallet.winRate7d.toFixed(1)}%
                  </td>
                  <td className="py-4 px-4 text-right text-white text-sm">
                    {wallet.transactions7d}
                  </td>
                  <td className="py-4 px-4 text-right text-white text-sm">
                    {formatNumber(wallet.volume7d)}K
                  </td>
                  <td className="py-4 px-4 text-right">
                    <div className="flex items-center justify-end space-x-3">
                      <span className="text-white font-medium text-sm">
                        {formatNumber(wallet.profit7d)}K
                      </span>
                      <div className="w-20 h-8">
                        <ProfitChart data={wallet.profit7dChart} />
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-right text-gray-400 text-sm">
                    {wallet.lastActivity}
                  </td>
                  <td className="py-4 px-6 text-right">
                    <button
                      onClick={() => copyToClipboard(wallet.walletAddress)}
                      className="bg-[#7FFFD4] text-black px-3 py-1 rounded-md text-xs font-medium hover:bg-[#6EEEC4] transition-colors"
                    >
                      Copy
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SmartWalletTable;
