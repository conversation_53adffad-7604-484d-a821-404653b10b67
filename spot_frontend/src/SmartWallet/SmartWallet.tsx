import React, { useState, useEffect } from 'react';
import Navbar from '@/Home/Navbar/Navbar';
import SmartWalletTable from './components/SmartWalletTable';
import FilterTabs from './components/FilterTabs';

export interface SmartWalletData {
  id: string;
  walletAddress: string;
  solBalance: number;
  pnl1d: number;
  pnl7d: number;
  pnl30d: number;
  winRate7d: number;
  transactions7d: number;
  volume7d: number;
  profit7d: number;
  profit7dChart: number[];
  lastActivity: string;
  category: string;
}

const SmartWallet = () => {
  const [activeFilter, setActiveFilter] = useState<string>('All');
  const [smartWalletData, setSmartWalletData] = useState<SmartWalletData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Mock data for demonstration - replace with actual API call
  const mockData: SmartWalletData[] = [
    {
      id: '1',
      walletAddress: '8Cag...1JPd',
      solBalance: 2500.7,
      pnl1d: 388.6,
      pnl7d: 846.7,
      pnl30d: 1904,
      winRate7d: 62.9,
      transactions7d: 57,
      volume7d: 16.5,
      profit7d: 16.5,
      profit7dChart: [12, 14, 13, 15, 16, 18, 16.5],
      lastActivity: '7h ago',
      category: 'Pump Smart Money'
    },
    {
      id: '2',
      walletAddress: 'HYWo...1ENp',
      solBalance: 1840,
      pnl1d: 271.2,
      pnl7d: 846.7,
      pnl30d: 3304,
      winRate7d: 41.7,
      transactions7d: 288,
      volume7d: 7.6,
      profit7d: 7.6,
      profit7dChart: [5, 6, 7, 8, 7.5, 7.8, 7.6],
      lastActivity: '4h ago',
      category: 'KOL'
    },
    {
      id: '3',
      walletAddress: '3pZS...mSdj',
      solBalance: 1560,
      pnl1d: 437.1,
      pnl7d: 244.1,
      pnl30d: 537.8,
      winRate7d: 63.6,
      transactions7d: 13,
      volume7d: 4.5,
      profit7d: 4.5,
      profit7dChart: [3, 4, 4.2, 4.8, 4.6, 4.7, 4.5],
      lastActivity: '5h ago',
      category: 'Early Sniper'
    },
    {
      id: '4',
      walletAddress: 'DMYx...Xhzj',
      solBalance: 890.3,
      pnl1d: 59.3,
      pnl7d: 222.5,
      pnl30d: 959.5,
      winRate7d: 81.5,
      transactions7d: 24,
      volume7d: 5.5,
      profit7d: 5.5,
      profit7dChart: [4.5, 5, 5.2, 5.8, 5.6, 5.7, 5.5],
      lastActivity: '5h ago',
      category: 'Long-Term Investor'
    },
    {
      id: '5',
      walletAddress: '3WG...P2xp',
      solBalance: 750,
      pnl1d: 61.6,
      pnl7d: 221.6,
      pnl30d: 456.3,
      winRate7d: 6.5,
      transactions7d: 51,
      volume7d: 2.5,
      profit7d: 2.5,
      profit7dChart: [2, 2.2, 2.4, 2.6, 2.5, 2.7, 2.5],
      lastActivity: '7h ago',
      category: 'Meme Expert'
    },
    {
      id: '6',
      walletAddress: 'B5Ru...FyLJ',
      solBalance: 1456,
      pnl1d: 164.5,
      pnl7d: 166.5,
      pnl30d: 391.8,
      winRate7d: 44.4,
      transactions7d: 445,
      volume7d: 1.5,
      profit7d: 1.5,
      profit7dChart: [1.2, 1.3, 1.4, 1.6, 1.5, 1.6, 1.5],
      lastActivity: '2h ago',
      category: 'Degen'
    },
    {
      id: '7',
      walletAddress: 'Anza...LxXH',
      solBalance: 1890,
      pnl1d: 61.8,
      pnl7d: 111.4,
      pnl30d: 170.6,
      winRate7d: 70.3,
      transactions7d: 897,
      volume7d: 2.4,
      profit7d: 2.4,
      profit7dChart: [2.1, 2.2, 2.3, 2.5, 2.4, 2.5, 2.4],
      lastActivity: '1h ago',
      category: 'AI Agent Expert'
    },
    {
      id: '8',
      walletAddress: '5dDJ...XhUA',
      solBalance: 1170,
      pnl1d: 117.6,
      pnl7d: 104.6,
      pnl30d: 588.2,
      winRate7d: 57.7,
      transactions7d: 261,
      volume7d: 2.5,
      profit7d: 2.5,
      profit7dChart: [2.2, 2.3, 2.4, 2.6, 2.5, 2.6, 2.5],
      lastActivity: '9h ago',
      category: 'Conservative Investor'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchSmartWalletData = async () => {
      setIsLoading(true);
      try {
        // Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setSmartWalletData(mockData);
      } catch (error) {
        console.error('Error fetching smart wallet data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSmartWalletData();
  }, []);

  const filteredData = smartWalletData.filter(wallet => {
    if (activeFilter === 'All') return true;
    return wallet.category === activeFilter;
  });

  return (
    <div className="min-h-screen flex flex-col bg-[#141416]">
      <Navbar />
      
      <main className="flex-1 overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full w-full">
          <div className="py-6 h-full w-full">
            {/* Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">Smart Wallet</h1>
              <p className="text-gray-400 text-sm">Track smart money movements and trading patterns</p>
            </div>

            {/* Filter Tabs */}
            <FilterTabs
              activeFilter={activeFilter}
              onFilterChange={setActiveFilter}
            />

            {/* Smart Wallet Table */}
            <div className="mt-6 w-full">
              <SmartWalletTable
                data={filteredData}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SmartWallet;
